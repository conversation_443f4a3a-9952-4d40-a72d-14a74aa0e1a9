#!/usr/bin/env python3
"""
🎯 SCHEMA-BASED BACKTESTING RULE PARSER

Parses structured JSON trading patterns from LLM and converts them into executable backtesting functions.
Supports <PERSON>'s situational analysis methodology with robust schema-based pattern processing.

Key Features:
- Parses structured JSON patterns (no more regex hell!)
- Generates Python functions compatible with walk-forward validation
- Handles complex multi-condition entry/exit logic
- Validates patterns against JSON schema
- Extensible condition evaluation system
"""

import json
import re
from dataclasses import dataclass, field
from typing import List, Optional, Callable, Dict, Any
import pandas as pd

# Optional config import for testing compatibility
try:
    from config import config
    WALKFORWARD_MIN_MULTIPLIER = config.WALKFORWARD_MIN_MULTIPLIER
except ImportError:
    # Default value for testing
    WALKFORWARD_MIN_MULTIPLIER = 1.5


class BacktestingRuleParseError(Exception):
    """Exception raised when backtesting rule parsing fails"""
    pass


@dataclass
class TradingPattern:
    """Represents a structured trading pattern from JSON schema"""
    pattern_name: str
    entry_conditions: List[Dict[str, Any]]
    exit_conditions: List[Dict[str, Any]]
    entry_logic: str = "AND"
    filters: List[Dict[str, Any]] = field(default_factory=list)
    position_sizing: Dict[str, Any] = field(default_factory=lambda: {"method": "fixed_percent", "value": config.DEFAULT_POSITION_SIZE_PCT / 100})
    description: str = ""
    market_situation: str = ""
    behavioral_logic: str = ""
    statistical_edge: Dict[str, Any] = field(default_factory=dict)
    optimal_conditions: Dict[str, Any] = field(default_factory=dict)
    implementation_notes: str = ""

    # Backward compatibility properties
    @property
    def rule_id(self) -> int:
        """Generate rule_id for backward compatibility"""
        return hash(self.pattern_name) % 10000

    @property
    def name(self) -> str:
        """Alias for pattern_name for backward compatibility"""
        return self.pattern_name

    @property
    def direction(self) -> str:
        """Extract direction from entry conditions for backward compatibility"""
        # Detect direction from entry conditions
        for condition in self.entry_conditions:
            condition_type = condition.get('condition', '')
            if condition_type in ['close_below_low', 'breakdown_below', 'gap_down', 'lower_low']:
                return "short"
            elif condition_type in ['close_above_high', 'breakout_above', 'gap_up', 'higher_high']:
                return "long"

        # Default to long if no clear direction indicators
        return "long"

    @property
    def entry_logic_text(self) -> str:
        """Convert entry conditions to text for backward compatibility"""
        if not self.entry_conditions:
            return "No conditions"

        condition_texts = []
        for condition in self.entry_conditions:
            condition_type = condition.get('condition', 'unknown')
            if condition_type == 'close_above_high':
                condition_texts.append('current_close > previous_high')
            elif condition_type == 'close_below_low':
                condition_texts.append('current_close < previous_low')
            elif condition_type == 'range_expansion':
                threshold = condition.get('threshold', 1.5)
                condition_texts.append(f'current_range > previous_range * {threshold}')
            else:
                condition_texts.append(f'{condition_type}')

        return f" {self.entry_logic} ".join(condition_texts)

    @property
    def stop_logic_text(self) -> str:
        """Convert exit conditions to stop logic text for backward compatibility"""
        for condition in self.exit_conditions:
            if condition['condition'] == 'fixed_stop_loss':
                percentage = condition.get('percentage', 0.02)
                return f'{percentage * 100}% stop loss'
            elif condition['condition'] == 'risk_reward_ratio':
                return 'previous_low'
        return 'previous_low'

    @property
    def target_logic_text(self) -> str:
        """Convert exit conditions to target logic text for backward compatibility"""
        for condition in self.exit_conditions:
            if condition['condition'] == 'fixed_take_profit':
                percentage = condition.get('percentage', 0.06)
                return f'{percentage * 100}% take profit'
            elif condition['condition'] == 'risk_reward_ratio':
                reward = condition.get('reward', 3)
                return f'entry_price + (entry_price - stop_price) * {reward}'
        return 'entry_price + (entry_price - stop_price) * 2.0'

    @property
    def position_size(self) -> float:
        """Extract position size for backward compatibility"""
        return self.position_sizing.get('value', 0.02) * 100  # Convert to percentage

    @property
    def timeframe(self) -> str:
        """Extract timeframe for backward compatibility"""
        optimal = self.optimal_conditions.get('timeframes', ['5m'])
        return optimal[0] if optimal else '5m'


class SchemaBasedPatternParser:
    """Schema-based parser that handles structured JSON patterns from LLM"""
    
    def __init__(self):
        self.patterns = []
        self.validation_errors = []
        self.supported_conditions = {
            # ORB-Specific Conditions
            'orb_breakout_above': self._orb_breakout_above,
            'orb_breakout_below': self._orb_breakout_below,
            'orb_breakout_above_or_below': self._orb_breakout_above_or_below,
            'opening_range_high': self._opening_range_high,
            'opening_range_low': self._opening_range_low,
            'close_above_orb_high': self._close_above_orb_high,
            'close_below_orb_low': self._close_below_orb_low,
            'orb_range_size': self._orb_range_size,
            'orb_time_filter': self._orb_time_filter,
            'multi_timeframe_orb_confirm': self._multi_timeframe_orb_confirm,
            'candles_since_session_start': self._candles_since_session_start,
            'session_filter': self._session_filter,

            # Legacy Price Action Conditions (for backward compatibility)
            'range_contraction': self._range_contraction,
            'range_expansion': self._range_expansion,
            'inside_day': self._inside_day,
            'outside_day': self._outside_day,
            'gap_up': self._gap_up,
            'gap_down': self._gap_down,
            'higher_high': self._higher_high,
            'lower_low': self._lower_low,
            'breakout_above': self._breakout_above,
            'breakdown_below': self._breakdown_below,
            'close_above_high': self._close_above_high,
            'close_below_low': self._close_below_low,

            # Volatility Conditions
            'low_volatility_regime': self._low_volatility_regime,
            'high_volatility_regime': self._high_volatility_regime,
            'volatility_expansion': self._volatility_expansion,
            'volatility_compression': self._volatility_compression,

            # Time-based Conditions
            'consecutive_days': self._consecutive_days,
            'day_of_week': self._day_of_week,
            'hour_of_day': self._hour_of_day,

            # Session/Filter Conditions
            'session_filter': self._session_filter,
            'time_filter': self._time_filter,
            'day_filter': self._day_filter,
            'trend_filter': self._trend_filter,
            'volume_filter': self._volume_filter,

            # Geometric Patterns
            'measured_move': self._measured_move,
            'retracement': self._retracement,
            'trend_continuation': self._trend_continuation,
            'trend_reversal': self._trend_reversal,
        }
        
        self.supported_exits = {
            'fixed_stop_loss': self._fixed_stop_loss,
            'fixed_take_profit': self._fixed_take_profit,
            'trailing_stop': self._trailing_stop,
            'risk_reward_ratio': self._risk_reward_ratio,
            'time_exit': self._time_exit,
            'pattern_failure': self._pattern_failure,
        }
    
    def parse_llm_response(self, llm_response: str) -> List[TradingPattern]:
        """Parse LLM JSON response into structured patterns"""
        self.patterns = []
        self.validation_errors = []
        
        try:
            # Try to parse as JSON first
            if llm_response.strip().startswith('{') or llm_response.strip().startswith('['):
                pattern_data = json.loads(llm_response)
                
                # Handle single pattern or array of patterns
                if isinstance(pattern_data, dict):
                    pattern_data = [pattern_data]
                
                for i, data in enumerate(pattern_data):
                    try:
                        pattern = self._create_pattern_from_json(data)
                        self.patterns.append(pattern)
                    except Exception as e:
                        self.validation_errors.append(f"Pattern {i+1} creation failed: {str(e)}")
            
            else:
                # Fallback: try to extract JSON from text response
                json_patterns = self._extract_json_from_text(llm_response)
                for i, json_str in enumerate(json_patterns):
                    try:
                        data = json.loads(json_str)
                        pattern = self._create_pattern_from_json(data)
                        self.patterns.append(pattern)
                    except Exception as e:
                        self.validation_errors.append(f"Pattern {i+1} JSON parsing failed: {str(e)}")
        
        except json.JSONDecodeError as e:
            raise BacktestingRuleParseError(f"UNBREAKABLE RULE VIOLATION: LLM must provide valid JSON format. Error: {e}")
        
        if not self.patterns:
            raise BacktestingRuleParseError("UNBREAKABLE RULE VIOLATION: No fallback allowed. LLM must provide properly formatted patterns.")
        
        return self.patterns
    
    def _extract_json_from_text(self, text: str) -> List[str]:
        """Extract JSON objects from text response"""
        json_objects = []
        
        # Look for JSON objects in the text
        json_pattern = r'\{[^{}]*(?:\{[^{}]*\}[^{}]*)*\}'
        matches = re.findall(json_pattern, text, re.DOTALL)
        
        for match in matches:
            # Try to validate it's proper JSON
            try:
                json.loads(match)
                json_objects.append(match)
            except json.JSONDecodeError:
                continue
        
        return json_objects
    
    def _create_pattern_from_json(self, data: Dict[str, Any]) -> TradingPattern:
        """Create TradingPattern from JSON data"""
        # Validate required fields
        required_fields = ['pattern_name', 'entry_conditions', 'exit_conditions']
        for field in required_fields:
            if field not in data:
                raise ValueError(f"Missing required field: {field}")
        
        return TradingPattern(
            pattern_name=data['pattern_name'],
            entry_conditions=data['entry_conditions'],
            exit_conditions=data['exit_conditions'],
            entry_logic=data.get('entry_logic', 'AND'),
            filters=data.get('filters', []),
            position_sizing=data.get('position_sizing', {"method": "fixed_percent", "value": config.DEFAULT_POSITION_SIZE_PCT / 100}),
            description=data.get('description', ''),
            market_situation=data.get('market_situation', ''),
            behavioral_logic=data.get('behavioral_logic', ''),
            statistical_edge=data.get('statistical_edge', {}),
            optimal_conditions=data.get('optimal_conditions', {}),
            implementation_notes=data.get('implementation_notes', '')
        )
    
    def generate_python_functions(self) -> List[Callable]:
        """Generate Python functions for backtesting from patterns"""
        functions = []
        
        for pattern in self.patterns:
            func = self._create_python_function(pattern)
            if func:
                functions.append(func)
        
        return functions
    
    def _create_python_function(self, pattern: TradingPattern) -> Optional[Callable]:
        """Create a Python function from a trading pattern"""
        
        def pattern_function(data, current_idx):
            """Generated pattern function"""
            if current_idx < 1 or current_idx >= len(data):
                return None
            
            current = data.iloc[current_idx]
            previous = data.iloc[current_idx - 1]
            
            # Check filters first
            for filter_condition in pattern.filters:
                if not self._evaluate_condition(filter_condition, data, current_idx):
                    return None
            
            # Evaluate entry conditions
            entry_results = []
            for condition in pattern.entry_conditions:
                result = self._evaluate_condition(condition, data, current_idx)
                entry_results.append(result)
            
            # Apply entry logic
            if pattern.entry_logic == 'AND':
                entry_signal = all(entry_results)
            elif pattern.entry_logic == 'OR':
                entry_signal = any(entry_results)
            else:
                entry_signal = all(entry_results)  # Default to AND
            
            if not entry_signal:
                return None
            
            # Determine trade direction and calculate precise ORB entry price
            direction, entry_price = self._calculate_orb_entry_price(pattern.entry_conditions, data, current_idx)

            if direction is None or entry_price is None:
                return None

            # Let backtester handle SL/TP calculation and validation
            # Just return the signal - backtester will handle the rest
            
            # Let backtester handle SL/TP and position sizing - just return basic signal
            return {
                'entry_price': entry_price,
                'direction': direction,
                'rule_id': hash(pattern.pattern_name) % 10000  # Generate ID from name
            }
        
        return pattern_function

    def _calculate_orb_entry_price(self, entry_conditions: List[Dict], data: pd.DataFrame, current_idx: int) -> tuple:
        """Calculate precise ORB entry price and direction - NO FALLBACKS"""

        # Look for ORB breakout conditions to determine entry price
        for condition in entry_conditions:
            condition_type = condition['condition']

            if condition_type == 'orb_breakout_above':
                # Long trade: Enter at ORB high + small buffer
                if 'opening_range_high' not in data.columns:
                    raise ValueError('UNBREAKABLE RULE VIOLATION: opening_range_high column missing from ORB data')

                orb_high = data['opening_range_high'].iloc[current_idx]
                if pd.isna(orb_high):
                    return None, None

                # Entry price is ORB high + 1 pip buffer for realistic execution
                entry_price = orb_high + 0.0001  # 1 pip buffer
                return 'long', entry_price

            elif condition_type == 'orb_breakout_below':
                # Short trade: Enter at ORB low - small buffer
                if 'opening_range_low' not in data.columns:
                    raise ValueError('UNBREAKABLE RULE VIOLATION: opening_range_low column missing from ORB data')

                orb_low = data['opening_range_low'].iloc[current_idx]
                if pd.isna(orb_low):
                    return None, None

                # Entry price is ORB low - 1 pip buffer for realistic execution
                entry_price = orb_low - 0.0001  # 1 pip buffer
                return 'short', entry_price

            elif condition_type == 'orb_breakout_above_or_below':
                # Bidirectional trade: Check which direction broke out
                if 'orb_breakout_up' in data.columns and data['orb_breakout_up'].iloc[current_idx]:
                    # Upward breakout
                    orb_high = data['opening_range_high'].iloc[current_idx]
                    if not pd.isna(orb_high):
                        entry_price = orb_high + 0.0001  # 1 pip buffer
                        return 'long', entry_price

                if 'orb_breakout_down' in data.columns and data['orb_breakout_down'].iloc[current_idx]:
                    # Downward breakout
                    orb_low = data['opening_range_low'].iloc[current_idx]
                    if not pd.isna(orb_low):
                        entry_price = orb_low - 0.0001  # 1 pip buffer
                        return 'short', entry_price

                return None, None  # No breakout detected

        # If no ORB conditions found, use close price as fallback (should not happen with ORB patterns)
        current_close = data['Close'].iloc[current_idx]
        return 'long', current_close

    def _evaluate_condition(self, condition: Dict[str, Any], data: pd.DataFrame, current_idx: int) -> bool:
        """Evaluate a single condition - UNBREAKABLE RULE: NO FALLBACKS"""
        condition_type = condition['condition']

        if condition_type in self.supported_conditions:
            # UNBREAKABLE RULE: Let BacktestingRuleParseError propagate - NO FALLBACKS!
            try:
                result = self.supported_conditions[condition_type](data, current_idx, condition)
                # Debug logging for condition evaluation
                if hasattr(self, '_debug_logging') and self._debug_logging:
                    print(f"      🔧 Condition '{condition_type}' at idx {current_idx}: {result}")
                return result
            except BacktestingRuleParseError:
                # UNBREAKABLE RULE: Re-raise BacktestingRuleParseError - NO FALLBACKS!
                raise
            except Exception as e:
                # Only catch other exceptions (programming errors, etc.)
                raise BacktestingRuleParseError(
                    f"UNBREAKABLE RULE VIOLATION: Condition '{condition_type}' failed with error: {e}. "
                    "System must fail rather than use fallbacks."
                )
        else:
            raise BacktestingRuleParseError(
                f"UNBREAKABLE RULE VIOLATION: Unsupported condition type '{condition_type}'. "
                f"Available conditions: {list(self.supported_conditions.keys())}. "
                "System must fail rather than use fallbacks."
            )
    
    # REMOVED: Manual position sizing - let backtester handle it
    
    # REMOVED: Manual SL/TP calculation - let backtester handle it

    # Condition Implementation Methods
    def _range_contraction(self, data: pd.DataFrame, current_idx: int, params: Dict) -> bool:
        """Check for range contraction over specified periods - FIXED: More restrictive threshold"""
        periods = params.get('periods', 4)
        threshold = params.get('threshold', config.RANGE_CONTRACTION_THRESHOLD)  # Configurable contraction threshold

        if current_idx < periods:
            return False

        ranges = data['High'] - data['Low']
        contractions_found = 0

        for i in range(1, periods + 1):
            if current_idx - i < 0:
                return False

            current_range = ranges.iloc[current_idx - i + 1]
            previous_range = ranges.iloc[current_idx - i]

            if previous_range == 0:
                continue

            contraction = (previous_range - current_range) / previous_range

            # Debug logging removed for cleaner output

            if contraction >= threshold:
                contractions_found += 1

        # FIXED: Require at least 2 out of 'periods' contractions to be more selective
        required_contractions = max(2, periods // 2)
        return contractions_found >= required_contractions

    def _range_expansion(self, data: pd.DataFrame, current_idx: int, params: Dict) -> bool:
        """Check for range expansion - FIXED: More restrictive threshold"""
        threshold = params.get('threshold', config.RANGE_EXPANSION_THRESHOLD)  # Configurable expansion threshold
        lookback = params.get('lookback', config.DEFAULT_LOOKBACK_PERIODS)  # Configurable lookback period

        if current_idx < lookback:
            return False

        current_range = data['High'].iloc[current_idx] - data['Low'].iloc[current_idx]
        avg_range = (data['High'].iloc[current_idx-lookback:current_idx] -
                    data['Low'].iloc[current_idx-lookback:current_idx]).mean()

        if avg_range == 0:
            return False

        expansion_ratio = current_range / avg_range

        # Debug logging removed for cleaner output

        return expansion_ratio > threshold

    def _close_above_high(self, data: pd.DataFrame, current_idx: int, params: Dict) -> bool:
        """Check if current close is above previous high"""
        _ = params  # Unused parameter for interface consistency
        if current_idx < 1:
            return False

        current_close = data['Close'].iloc[current_idx]
        previous_high = data['High'].iloc[current_idx - 1]

        return current_close > previous_high

    def _close_below_high(self, data: pd.DataFrame, current_idx: int, params: Dict) -> bool:
        """Check if current close is below previous high"""
        _ = params  # Unused parameter for interface consistency
        if current_idx < 1:
            return False

        current_close = data['Close'].iloc[current_idx]
        previous_high = data['High'].iloc[current_idx - 1]

        return current_close < previous_high

    def _close_below_low(self, data: pd.DataFrame, current_idx: int, params: Dict) -> bool:
        """Check if current close is below previous low"""
        _ = params  # Unused parameter for interface consistency
        if current_idx < 1:
            return False

        current_close = data['Close'].iloc[current_idx]
        previous_low = data['Low'].iloc[current_idx - 1]

        return current_close < previous_low

    def _inside_day(self, data: pd.DataFrame, current_idx: int, params: Dict) -> bool:
        """Check for inside day pattern"""
        _ = params  # Unused parameter for interface consistency
        if current_idx < 1:
            return False

        current_high = data['High'].iloc[current_idx]
        current_low = data['Low'].iloc[current_idx]
        previous_high = data['High'].iloc[current_idx - 1]
        previous_low = data['Low'].iloc[current_idx - 1]

        return current_high < previous_high and current_low > previous_low

    def _outside_day(self, data: pd.DataFrame, current_idx: int, params: Dict) -> bool:
        """Check for outside day pattern"""
        _ = params  # Unused parameter for interface consistency
        if current_idx < 1:
            return False

        current_high = data['High'].iloc[current_idx]
        current_low = data['Low'].iloc[current_idx]
        previous_high = data['High'].iloc[current_idx - 1]
        previous_low = data['Low'].iloc[current_idx - 1]

        return current_high > previous_high and current_low < previous_low

    def _low_volatility_regime(self, data: pd.DataFrame, current_idx: int, params: Dict) -> bool:
        """Check if in low volatility regime - FIXED: More restrictive threshold"""
        lookback = params.get('lookback', config.DEFAULT_LOOKBACK_PERIODS)
        threshold = params.get('threshold', config.LOW_VOLATILITY_THRESHOLD)  # Configurable low volatility threshold

        if current_idx < lookback:
            return False

        returns = data['Close'].iloc[current_idx-lookback:current_idx+1].pct_change().dropna()
        volatility = returns.std()

        # Debug logging removed for cleaner output

        return volatility < threshold

    def _volatility_expansion(self, data: pd.DataFrame, current_idx: int, params: Dict) -> bool:
        """Check for volatility expansion"""
        return self._range_expansion(data, current_idx, params)

    def _consecutive_days(self, data: pd.DataFrame, current_idx: int, params: Dict) -> bool:
        """Check for consecutive days meeting criteria"""
        criteria_type = params.get('type', 'range_contraction')
        periods = params.get('periods', 3)

        if current_idx < periods:
            return False

        # Delegate to the specific criteria type
        if criteria_type == 'range_contraction':
            return self._range_contraction(data, current_idx, params)
        elif criteria_type == 'higher_high':
            # Check for consecutive higher highs
            for i in range(periods - 1):
                if data['High'].iloc[current_idx - i] <= data['High'].iloc[current_idx - i - 1]:
                    return False
            return True
        elif criteria_type == 'inside_day':
            # Check for consecutive inside days
            for i in range(periods):
                if not self._inside_day(data, current_idx - i, {}):
                    return False
            return True
        else:
            return False

    # Implemented condition methods
    def _gap_up(self, data: pd.DataFrame, current_idx: int, params: Dict) -> bool:
        """Check for gap up - CFD-appropriate percentage-based detection"""
        if current_idx < 1:
            return False

        # CFD-appropriate threshold (0.1% default for leveraged instruments)
        threshold = params.get('threshold', 0.001)  # 0.1% default

        current_open = data['Open'].iloc[current_idx]
        previous_close = data['Close'].iloc[current_idx - 1]

        # Calculate percentage gap
        if previous_close == 0:
            return False

        gap = (current_open - previous_close) / previous_close
        return gap > threshold

    def _gap_down(self, data: pd.DataFrame, current_idx: int, params: Dict) -> bool:
        """Check for gap down - CFD-appropriate percentage-based detection"""
        if current_idx < 1:
            return False

        # CFD-appropriate threshold (0.1% default for leveraged instruments)
        threshold = params.get('threshold', 0.001)  # 0.1% default

        current_open = data['Open'].iloc[current_idx]
        previous_close = data['Close'].iloc[current_idx - 1]

        # Calculate percentage gap
        if previous_close == 0:
            return False

        gap = (previous_close - current_open) / previous_close
        return gap > threshold

    def _higher_high(self, data: pd.DataFrame, current_idx: int, params: Dict) -> bool:
        """Check for higher high"""
        _ = params  # Unused parameter for interface consistency
        if current_idx < 1:
            return False

        current_high = data['High'].iloc[current_idx]
        previous_high = data['High'].iloc[current_idx - 1]

        return current_high > previous_high

    def _lower_low(self, data: pd.DataFrame, current_idx: int, params: Dict) -> bool:
        """Check for lower low"""
        _ = params  # Unused parameter for interface consistency
        if current_idx < 1:
            return False

        current_low = data['Low'].iloc[current_idx]
        previous_low = data['Low'].iloc[current_idx - 1]

        return current_low < previous_low

    def _close_above_high(self, data: pd.DataFrame, current_idx: int, params: Dict) -> bool:
        """Check if current close is above previous high - basic breakout condition"""
        lookback = params.get('lookback', 1)

        if current_idx < lookback:
            return False

        current_close = data['Close'].iloc[current_idx]
        previous_high = data['High'].iloc[current_idx - lookback]

        return current_close > previous_high

    def _close_below_low(self, data: pd.DataFrame, current_idx: int, params: Dict) -> bool:
        """Check if current close is below previous low - basic breakdown condition"""
        lookback = params.get('lookback', 1)

        if current_idx < lookback:
            return False

        current_close = data['Close'].iloc[current_idx]
        previous_low = data['Low'].iloc[current_idx - lookback]

        return current_close < previous_low

    def _breakout_above(self, data: pd.DataFrame, current_idx: int, params: Dict) -> bool:
        """DEPRECATED: Use orb_breakout_above instead - NO FALLBACKS"""
        raise ValueError('UNBREAKABLE RULE VIOLATION: _breakout_above is deprecated, use orb_breakout_above instead')

    def _breakdown_below(self, data: pd.DataFrame, current_idx: int, params: Dict) -> bool:
        """DEPRECATED: Use orb_breakout_below instead - NO FALLBACKS"""
        raise ValueError('UNBREAKABLE RULE VIOLATION: _breakdown_below is deprecated, use orb_breakout_below instead')

    def _high_volatility_regime(self, data: pd.DataFrame, current_idx: int, params: Dict) -> bool:
        return not self._low_volatility_regime(data, current_idx, params)

    def _volatility_compression(self, data: pd.DataFrame, current_idx: int, params: Dict) -> bool:
        return not self._volatility_expansion(data, current_idx, params)

    def _day_of_week(self, data: pd.DataFrame, current_idx: int, params: Dict) -> bool:
        """DEPRECATED: Use candles_since_session_start instead - NO FALLBACKS"""
        raise ValueError('UNBREAKABLE RULE VIOLATION: _day_of_week is deprecated, use candles_since_session_start instead')

    def _candles_since_session_start(self, data: pd.DataFrame, current_idx: int, params: Dict) -> bool:
        """Check if current candle is within specified number of candles since session start - NO FALLBACKS"""
        if current_idx < 0 or current_idx >= len(data):
            return False

        # Get target candle count from params
        max_candles = params.get('value')
        if max_candles is None:
            raise ValueError('UNBREAKABLE RULE VIOLATION: candles_since_session_start condition requires "value" parameter')

        # Use pre-calculated session_candle_number if available
        if 'session_candle_number' in data.columns:
            current_candle_number = data['session_candle_number'].iloc[current_idx]
            if pd.isna(current_candle_number):
                return False
            return int(current_candle_number) <= int(max_candles)

        # If session_candle_number not available, fail hard
        raise ValueError('UNBREAKABLE RULE VIOLATION: session_candle_number column missing from ORB data')

    def _orb_breakout_above_or_below(self, data: pd.DataFrame, current_idx: int, params: Dict) -> bool:
        """Check if price breaks above OR below opening range using pre-calculated ORB data - NO FALLBACKS"""
        if current_idx < 1:
            return False

        # Check both directions using pre-calculated signals
        breakout_up = False
        breakout_down = False

        if 'orb_breakout_up' in data.columns:
            breakout_up = bool(data['orb_breakout_up'].iloc[current_idx])

        if 'orb_breakout_down' in data.columns:
            breakout_down = bool(data['orb_breakout_down'].iloc[current_idx])

        # Return True if breakout in either direction
        return breakout_up or breakout_down

    def _hour_of_day(self, data: pd.DataFrame, current_idx: int, params: Dict) -> bool:
        """DEPRECATED: Use candles_since_session_start instead - NO FALLBACKS"""
        raise ValueError('UNBREAKABLE RULE VIOLATION: hour_of_day is deprecated, use candles_since_session_start instead')

    def _measured_move(self, data: pd.DataFrame, current_idx: int, params: Dict) -> bool:
        """DEPRECATED: Use ORB conditions instead - NO FALLBACKS"""
        raise ValueError('UNBREAKABLE RULE VIOLATION: _measured_move is deprecated, use ORB conditions instead')

    def _retracement(self, data: pd.DataFrame, current_idx: int, params: Dict) -> bool:
        """DEPRECATED: Use ORB conditions instead - NO FALLBACKS"""
        raise ValueError('UNBREAKABLE RULE VIOLATION: _retracement is deprecated, use ORB conditions instead')

    def _trend_continuation(self, data: pd.DataFrame, current_idx: int, params: Dict) -> bool:
        """DEPRECATED: Use ORB conditions instead - NO FALLBACKS"""
        raise ValueError('UNBREAKABLE RULE VIOLATION: _trend_continuation is deprecated, use ORB conditions instead')

    def _trend_reversal(self, data: pd.DataFrame, current_idx: int, params: Dict) -> bool:
        """DEPRECATED: Use ORB conditions instead - NO FALLBACKS"""
        raise ValueError('UNBREAKABLE RULE VIOLATION: _trend_reversal is deprecated, use ORB conditions instead')

    # ============================================================================
    # ORB-SPECIFIC CONDITIONS (Opening Range Breakout)
    # ============================================================================

    def _orb_breakout_above(self, data: pd.DataFrame, current_idx: int, params: Dict) -> bool:
        """Check if price breaks above opening range high using pre-calculated ORB data - NO FALLBACKS"""
        if current_idx < 1:
            return False

        # Use pre-calculated ORB breakout signal if available
        if 'orb_breakout_up' in data.columns:
            return bool(data['orb_breakout_up'].iloc[current_idx])

        # Use pre-calculated opening range high - REQUIRED
        if 'opening_range_high' not in data.columns:
            raise ValueError('UNBREAKABLE RULE VIOLATION: opening_range_high column missing from ORB data')

        current_close = data['Close'].iloc[current_idx]
        orb_high = data['opening_range_high'].iloc[current_idx]

        if pd.isna(orb_high):
            return False

        return current_close > orb_high

    def _orb_breakout_below(self, data: pd.DataFrame, current_idx: int, params: Dict) -> bool:
        """Check if price breaks below opening range low using pre-calculated ORB data - NO FALLBACKS"""
        if current_idx < 1:
            return False

        # Use pre-calculated ORB breakout signal if available
        if 'orb_breakout_down' in data.columns:
            return bool(data['orb_breakout_down'].iloc[current_idx])

        # Use pre-calculated opening range low - REQUIRED
        if 'opening_range_low' not in data.columns:
            raise ValueError('UNBREAKABLE RULE VIOLATION: opening_range_low column missing from ORB data')

        current_close = data['Close'].iloc[current_idx]
        orb_low = data['opening_range_low'].iloc[current_idx]

        if pd.isna(orb_low):
            return False

        return current_close < orb_low

    def _calculate_dynamic_orb_high(self, data: pd.DataFrame, current_idx: int, orb_period_bars: int, session: str = 'london') -> float:
        """
        Calculate opening range high dynamically from OHLCV data.

        Args:
            data: OHLCV DataFrame with DatetimeIndex
            current_idx: Current bar index
            orb_period_bars: Number of bars for opening range (1-6)
            session: Trading session ('london', 'ny', 'asian', 'all')

        Returns:
            Opening range high or NaN if cannot be calculated
        """
        import numpy as np

        if current_idx < orb_period_bars:
            return np.nan

        # Get current timestamp
        if not isinstance(data.index, pd.DatetimeIndex):
            # Fallback: use simple lookback if no datetime index
            start_idx = max(0, current_idx - orb_period_bars)
            opening_bars = data.iloc[start_idx:current_idx]
            return opening_bars['High'].max()

        current_time = data.index[current_idx]

        # Use simple daily session logic until we integrate backtester's session API
        current_date = current_time.date()
        session_start = pd.Timestamp.combine(current_date, pd.Timestamp("00:00").time())
        if current_time.tz:
            session_start = session_start.tz_localize(current_time.tz)

        # Find bars within the session that occurred before current bar
        session_data = data[(data.index >= session_start) & (data.index < current_time)]

        if len(session_data) < orb_period_bars:
            return np.nan

        # Take first N bars of the session for opening range
        opening_bars = session_data.head(orb_period_bars)
        return opening_bars['High'].max()

    def _calculate_dynamic_orb_low(self, data: pd.DataFrame, current_idx: int, orb_period_bars: int, session: str = 'london') -> float:
        """
        Calculate opening range low dynamically from OHLCV data.

        Args:
            data: OHLCV DataFrame with DatetimeIndex
            current_idx: Current bar index
            orb_period_bars: Number of bars for opening range (1-6)
            session: Trading session ('london', 'ny', 'asian', 'all')

        Returns:
            Opening range low or NaN if cannot be calculated
        """
        import numpy as np

        if current_idx < orb_period_bars:
            return np.nan

        # Get current timestamp
        if not isinstance(data.index, pd.DatetimeIndex):
            # Fallback: use simple lookback if no datetime index
            start_idx = max(0, current_idx - orb_period_bars)
            opening_bars = data.iloc[start_idx:current_idx]
            return opening_bars['Low'].min()

        current_time = data.index[current_idx]

        # Use simple daily session logic until we integrate backtester's session API
        current_date = current_time.date()
        session_start = pd.Timestamp.combine(current_date, pd.Timestamp("00:00").time())
        if current_time.tz:
            session_start = session_start.tz_localize(current_time.tz)

        # Find bars within the session that occurred before current bar
        session_data = data[(data.index >= session_start) & (data.index < current_time)]

        if len(session_data) < orb_period_bars:
            return np.nan

        # Take first N bars of the session for opening range
        opening_bars = session_data.head(orb_period_bars)
        return opening_bars['Low'].min()

    # REMOVED: Manual session logic - use backtester's built-in session functionality

    def _calculate_flexible_orb_high(self, data: pd.DataFrame, current_idx: int, bar_count: int) -> float:
        """Calculate opening range high based on flexible bar count (1st, 2nd, 3rd candle, etc.)"""
        import numpy as np

        # Get the current date to find session start
        if 'date' not in data.columns:
            # Fallback: use session detection based on hour gaps
            return self._calculate_session_orb_high(data, current_idx, bar_count)

        current_date = data['date'].iloc[current_idx] if 'date' in data.columns else data.index[current_idx].date()

        # Find all bars for this date
        if 'date' in data.columns:
            session_data = data[data['date'] == current_date]
        else:
            # Fallback: assume daily sessions
            session_start = data.index[current_idx].replace(hour=0, minute=0, second=0, microsecond=0)
            session_end = session_start + pd.Timedelta(days=1)
            session_data = data[(data.index >= session_start) & (data.index < session_end)]

        if len(session_data) < bar_count:
            return np.nan

        # Calculate opening range high from first N bars of session
        opening_bars = session_data.head(bar_count)
        return opening_bars['High'].max()

    def _calculate_flexible_orb_low(self, data: pd.DataFrame, current_idx: int, bar_count: int) -> float:
        """Calculate opening range low based on flexible bar count (1st, 2nd, 3rd candle, etc.)"""
        import numpy as np

        # Get the current date to find session start
        if 'date' not in data.columns:
            # Fallback: use session detection based on hour gaps
            return self._calculate_session_orb_low(data, current_idx, bar_count)

        current_date = data['date'].iloc[current_idx] if 'date' in data.columns else data.index[current_idx].date()

        # Find all bars for this date
        if 'date' in data.columns:
            session_data = data[data['date'] == current_date]
        else:
            # Fallback: assume daily sessions
            session_start = data.index[current_idx].replace(hour=0, minute=0, second=0, microsecond=0)
            session_end = session_start + pd.Timedelta(days=1)
            session_data = data[(data.index >= session_start) & (data.index < session_end)]

        if len(session_data) < bar_count:
            return np.nan

        # Calculate opening range low from first N bars of session
        opening_bars = session_data.head(bar_count)
        return opening_bars['Low'].min()

    def _calculate_session_orb_high(self, data: pd.DataFrame, current_idx: int, bar_count: int) -> float:
        """Fallback: Calculate ORB high using session detection"""
        import numpy as np

        # Simple fallback: use previous N bars
        if current_idx < bar_count:
            return np.nan

        start_idx = max(0, current_idx - bar_count)
        opening_bars = data.iloc[start_idx:current_idx]
        return opening_bars['High'].max()

    def _calculate_session_orb_low(self, data: pd.DataFrame, current_idx: int, bar_count: int) -> float:
        """Fallback: Calculate ORB low using session detection"""
        import numpy as np

        # Simple fallback: use previous N bars
        if current_idx < bar_count:
            return np.nan

        start_idx = max(0, current_idx - bar_count)
        opening_bars = data.iloc[start_idx:current_idx]
        return opening_bars['Low'].min()

    def _opening_range_high(self, data: pd.DataFrame, current_idx: int, params: Dict) -> bool:
        """Check if opening range high is available in pre-calculated data - NO FALLBACKS"""
        if 'opening_range_high' not in data.columns:
            raise ValueError('UNBREAKABLE RULE VIOLATION: opening_range_high column missing from ORB data')
        return not pd.isna(data['opening_range_high'].iloc[current_idx])

    def _opening_range_low(self, data: pd.DataFrame, current_idx: int, params: Dict) -> bool:
        """Check if opening range low is available in pre-calculated data - NO FALLBACKS"""
        if 'opening_range_low' not in data.columns:
            raise ValueError('UNBREAKABLE RULE VIOLATION: opening_range_low column missing from ORB data')
        return not pd.isna(data['opening_range_low'].iloc[current_idx])

    def _close_above_orb_high(self, data: pd.DataFrame, current_idx: int, params: Dict) -> bool:
        """Check if close is above opening range high"""
        return self._orb_breakout_above(data, current_idx, params)

    def _close_below_orb_low(self, data: pd.DataFrame, current_idx: int, params: Dict) -> bool:
        """Check if close is below opening range low"""
        return self._orb_breakout_below(data, current_idx, params)

    def _orb_range_size(self, data: pd.DataFrame, current_idx: int, params: Dict) -> bool:
        """Check if opening range size meets criteria using pre-calculated data - NO FALLBACKS"""
        if 'opening_range_high' not in data.columns or 'opening_range_low' not in data.columns:
            raise ValueError('UNBREAKABLE RULE VIOLATION: opening_range_high/low columns missing from ORB data')

        orb_high = data['opening_range_high'].iloc[current_idx]
        orb_low = data['opening_range_low'].iloc[current_idx]

        if pd.isna(orb_high) or pd.isna(orb_low):
            return False

        orb_size = orb_high - orb_low
        min_size = params.get('min_size', 0.0001)  # 1 pip minimum
        max_size = params.get('max_size', 0.01)    # 100 pips maximum

        return min_size <= orb_size <= max_size

    def _orb_time_filter(self, data: pd.DataFrame, current_idx: int, params: Dict) -> bool:
        """Filter based on ORB session timing"""
        if current_idx < 0 or current_idx >= len(data):
            return False

        # Check if we have hour information
        if 'hour' not in data.columns:
            return True  # Allow if no time filtering available

        current_hour = data['hour'].iloc[current_idx]
        start_hour = params.get('start_hour', 8)
        end_hour = params.get('end_hour', 16)

        return start_hour <= current_hour <= end_hour

    def _multi_timeframe_orb_confirm(self, data: pd.DataFrame, current_idx: int, params: Dict) -> bool:
        """Multi-timeframe ORB confirmation (simplified implementation)"""
        # For now, just check if ORB breakout exists
        return (self._orb_breakout_above(data, current_idx, params) or
                self._orb_breakout_below(data, current_idx, params))

    # ============================================================================
    # FILTER CONDITIONS (Session, Time, Volume, etc.)
    # ============================================================================

    def _session_filter(self, data: pd.DataFrame, current_idx: int, params: Dict) -> bool:
        """Filter based on trading session - USE BACKTESTER'S BUILT-IN SESSION LOGIC"""
        if current_idx < 0 or current_idx >= len(data):
            return False

        session_value = params.get('value', 'all').lower()

        # For now, use simple time-based filtering until we find the backtester's session API
        if not isinstance(data.index, pd.DatetimeIndex):
            raise BacktestingRuleParseError(
                "UNBREAKABLE RULE VIOLATION: session_filter requires DatetimeIndex. "
                "Current data lacks timestamp information. System must fail rather than use fallbacks."
            )

        current_time = data.index[current_idx]
        current_hour = current_time.hour

        # Use backtester's session definitions
        if session_value == 'london':
            return 8 <= current_hour < 16  # 8:00 - 16:00 GMT
        elif session_value == 'ny':
            return 13 <= current_hour < 21  # 13:00 - 21:00 GMT
        elif session_value == 'asian':
            return current_hour >= 22 or current_hour < 8  # 22:00 - 8:00 GMT
        elif session_value in ['all', 'any']:
            return True
        else:
            raise BacktestingRuleParseError(
                f"UNBREAKABLE RULE VIOLATION: Unsupported session_filter value '{session_value}'. "
                "Supported values: all, any, london, ny, asian. System must fail rather than use fallbacks."
            )

    def _time_filter(self, data: pd.DataFrame, current_idx: int, params: Dict) -> bool:
        """Filter based on time of day - PROPERLY IMPLEMENTED"""
        if current_idx < 0 or current_idx >= len(data):
            return False

        # Check if we have DateTime column or datetime index
        has_datetime_index = isinstance(data.index, pd.DatetimeIndex)
        has_datetime_column = 'DateTime' in data.columns

        if not has_datetime_index and not has_datetime_column:
            raise BacktestingRuleParseError(
                "UNBREAKABLE RULE VIOLATION: time_filter requires DateTime column or DatetimeIndex. "
                "Current data lacks timestamp information. System must fail rather than use fallbacks."
            )

        # Get current time - use index if it's datetime, otherwise DateTime column
        if has_datetime_index:
            current_time = data.index[current_idx]
        else:
            current_time = data['DateTime'].iloc[current_idx]

        # Get time filter parameters
        start_hour = params.get('start_hour', 0)
        end_hour = params.get('end_hour', 23)
        start_minute = params.get('start_minute', 0)
        end_minute = params.get('end_minute', 59)

        current_hour = current_time.hour
        current_minute = current_time.minute

        # Check if current time is within the specified range
        start_time_minutes = start_hour * 60 + start_minute
        end_time_minutes = end_hour * 60 + end_minute
        current_time_minutes = current_hour * 60 + current_minute

        return start_time_minutes <= current_time_minutes <= end_time_minutes

    def _day_filter(self, data: pd.DataFrame, current_idx: int, params: Dict) -> bool:
        """Filter based on day of week - PROPERLY IMPLEMENTED"""
        if current_idx < 0 or current_idx >= len(data):
            return False

        # Check if we have DateTime column or datetime index
        has_datetime_index = isinstance(data.index, pd.DatetimeIndex)
        has_datetime_column = 'DateTime' in data.columns

        if not has_datetime_index and not has_datetime_column:
            raise BacktestingRuleParseError(
                "UNBREAKABLE RULE VIOLATION: day_filter requires DateTime column or DatetimeIndex. "
                "Current data lacks timestamp information. System must fail rather than use fallbacks."
            )

        # Get current time - use index if it's datetime, otherwise DateTime column
        if has_datetime_index:
            current_time = data.index[current_idx]
        else:
            current_time = data['DateTime'].iloc[current_idx]

        # Get day filter parameters
        allowed_days = params.get('days', ['monday', 'tuesday', 'wednesday', 'thursday', 'friday'])
        if isinstance(allowed_days, str):
            allowed_days = [allowed_days]

        # Convert to lowercase for comparison
        allowed_days = [day.lower() for day in allowed_days]

        # Get current day of week (0=Monday, 6=Sunday)
        current_day_num = current_time.weekday()
        day_names = ['monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday', 'sunday']
        current_day_name = day_names[current_day_num]

        return current_day_name in allowed_days

    def _trend_filter(self, data: pd.DataFrame, current_idx: int, params: Dict) -> bool:
        """Filter based on overall trend direction"""
        if current_idx < 20:
            raise BacktestingRuleParseError(
                "UNBREAKABLE RULE VIOLATION: trend_filter requires at least 20 data points for calculation. "
                f"Current index {current_idx} is insufficient. System must fail rather than use fallbacks."
            )

        lookback = params.get('lookback', config.DEFAULT_LOOKBACK_PERIODS)
        threshold = params.get('threshold', config.HIGH_VOLATILITY_THRESHOLD)

        if current_idx < lookback:
            raise BacktestingRuleParseError(
                f"UNBREAKABLE RULE VIOLATION: trend_filter requires {lookback} lookback periods. "
                f"Current index {current_idx} is insufficient. System must fail rather than use fallbacks."
            )

        # Simple trend filter: check if price is above/below moving average
        ma = data['Close'].iloc[current_idx - lookback:current_idx].mean()
        current_price = data['Close'].iloc[current_idx]

        trend_strength = (current_price - ma) / ma
        return abs(trend_strength) > threshold

    def _volume_filter(self, data: pd.DataFrame, current_idx: int, params: Dict) -> bool:
        """Filter based on volume conditions - UNBREAKABLE RULE: NO FALLBACKS"""
        if 'Volume' not in data.columns:
            raise BacktestingRuleParseError(
                "UNBREAKABLE RULE VIOLATION: volume_filter requires Volume column in data. "
                "Current data lacks volume information. System must fail rather than use fallbacks. "
                "Either provide data with Volume column or remove volume_filter from pattern."
            )

        if current_idx < 1:
            raise BacktestingRuleParseError(
                "UNBREAKABLE RULE VIOLATION: volume_filter requires at least 1 previous data point. "
                f"Current index {current_idx} is insufficient. System must fail rather than use fallbacks."
            )

        threshold = params.get('threshold', config.MEASURED_MOVE_THRESHOLD)
        lookback = params.get('lookback', config.DEFAULT_LOOKBACK_PERIODS)

        if current_idx < lookback:
            raise BacktestingRuleParseError(
                f"UNBREAKABLE RULE VIOLATION: volume_filter requires {lookback} lookback periods. "
                f"Current index {current_idx} is insufficient. System must fail rather than use fallbacks."
            )

        avg_volume = data['Volume'].iloc[current_idx - lookback:current_idx].mean()
        current_volume = data['Volume'].iloc[current_idx]

        return current_volume > (avg_volume * threshold)

    # Exit condition methods (placeholders)
    def _fixed_stop_loss(self, data: pd.DataFrame, position, params: Dict) -> bool:
        return False

    def _fixed_take_profit(self, data: pd.DataFrame, position, params: Dict) -> bool:
        return False

    def _trailing_stop(self, data: pd.DataFrame, position, params: Dict) -> bool:
        return False

    def _risk_reward_ratio(self, data: pd.DataFrame, position, params: Dict) -> bool:
        return False

    def _time_exit(self, data: pd.DataFrame, position, params: Dict) -> bool:
        return False

    def _pattern_failure(self, data: pd.DataFrame, position, params: Dict) -> bool:
        return False

    def _extract_patterns(self, llm_response: str) -> List[str]:
        """Extract individual patterns for backward compatibility with cortex.py"""
        try:
            patterns = self.parse_llm_response(llm_response)
            # Return pattern names as strings for compatibility
            return [pattern.pattern_name for pattern in patterns]
        except Exception:
            # Fallback: return the whole response as a single pattern
            return [llm_response]


# Backward compatibility factory function
def BacktestingTradingRule(pattern_name=None, entry_logic_text=None, stop_logic_text=None,
                          target_logic_text=None, direction=None, position_size=None,
                          timeframe=None, rule_id=None, **kwargs):
    """Factory function for backward compatibility with old constructor signature"""

    # Convert old parameters to new schema format
    entry_conditions = []
    exit_conditions = []

    # Parse entry logic text to conditions
    if entry_logic_text:
        if "close > high" in entry_logic_text.lower():
            entry_conditions.append({"condition": "close_above_high", "lookback": 1})
        elif "close < low" in entry_logic_text.lower():
            entry_conditions.append({"condition": "close_below_low", "lookback": 1})
        else:
            entry_conditions.append({"condition": "custom", "description": entry_logic_text})

    # Parse exit logic to conditions
    if target_logic_text and "entry_price" in target_logic_text:
        # Extract risk-reward ratio if present
        if "*" in target_logic_text:
            try:
                parts = target_logic_text.split("*")
                if len(parts) > 1:
                    reward = float(parts[1].strip())
                    exit_conditions.append({"condition": "risk_reward_ratio", "risk": 1, "reward": reward})
            except:
                exit_conditions.append({"condition": "fixed_take_profit", "percentage": 0.03})
        else:
            exit_conditions.append({"condition": "fixed_take_profit", "percentage": 0.03})

    # Default conditions if none provided
    if not entry_conditions:
        entry_conditions = [{"condition": "close_above_high", "lookback": 1}]
    if not exit_conditions:
        exit_conditions = [{"condition": "risk_reward_ratio", "risk": 1, "reward": 2}]

    # Convert position size
    position_sizing = {"method": "fixed_percent", "value": (position_size or 2.0) / 100}

    # Filter out unsupported kwargs
    supported_kwargs = {}
    unsupported_params = ['name', 'rule_id', 'market_logic', 'entry_logic', 'stop_logic', 'target_logic']
    for key, value in kwargs.items():
        if key not in unsupported_params:  # Filter out old parameters
            supported_kwargs[key] = value

    # Create the new pattern
    return TradingPattern(
        pattern_name=pattern_name or "Legacy Pattern",
        entry_conditions=entry_conditions,
        exit_conditions=exit_conditions,
        position_sizing=position_sizing,
        optimal_conditions={"timeframes": [timeframe or "5m"]},
        **supported_kwargs
    )

# Backward compatibility aliases
BacktestingRuleParser = SchemaBasedPatternParser


# Backward compatibility functions
def parse_backtesting_rules(llm_response: str) -> List[Callable]:
    """Parse LLM response and return pattern functions using schema-based parsing"""
    parser = SchemaBasedPatternParser()
    try:
        parser.parse_llm_response(llm_response)
        return parser.generate_python_functions()
    except BacktestingRuleParseError as e:
        print(f"Schema-based pattern parsing failed: {e}")
        return []
